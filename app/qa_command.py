import logging
import traceback
from typing import <PERSON><PERSON>, Optional
import psycopg

from .utils import get_robot_by_channel
from .jira_client import create_qa_ticket
from .info_command import validate_robot_serial, get_robot_info_from_portal, get_robot_status_from_portal

logger = logging.getLogger(__name__)


def handle_qa_command(
    portal_conn: psycopg.Connection,
    channel_name: str,
    user_name: str,
    command_text: str,
    channel_id: Optional[str] = None,
    message_ts: Optional[str] = None,
) -> Tuple[bool, str]:
    try:
        # Parse the command arguments
        if not command_text:
            description, robot_serial = "", None
        else:
            # Split the command text into words
            parts = command_text.strip().split()

            if not parts:
                description, robot_serial = "", None
            else:
                # Check if the first part looks like a robot serial
                first_part = parts[0].lower()
                if (first_part.startswith("slayer") or first_part.startswith("reaper")) and len(parts) > 1:
                    # First part is robot serial, rest is description
                    robot_serial = parts[0]
                    description = " ".join(parts[1:])
                else:
                    # All parts are description
                    description = " ".join(parts)
                    robot_serial = None

        # Validate that description is provided
        if not description or description.strip() == "":
            error_msg = "Error: Please provide a description for the QA ticket: `/qa <description>`"
            return False, error_msg

        # Determine robot serial
        detected_robot = None
        if robot_serial:
            # Robot was specified as argument - validate it
            if not validate_robot_serial(robot_serial):
                error_msg = f"Error: Invalid robot serial format '{robot_serial}'. Expected format: slayer# or reaper#"
                return False, error_msg
            detected_robot = robot_serial
            logger.info(f"User @{user_name} created QA ticket for specified robot {robot_serial} in channel {channel_name}")
        else:
            # No robot specified, try to determine from channel
            robot_from_channel = get_robot_by_channel(portal_conn, channel_name)
            if robot_from_channel:
                detected_robot = robot_from_channel
                logger.info(f"User @{user_name} created QA ticket for channel robot {detected_robot} in channel {channel_name}")
            else:
                # No robot found from channel - that's okay, we'll create the ticket without robot info
                logger.info(f"User @{user_name} created QA ticket with no robot association in channel {channel_name}")

        # Get robot information if we have a robot serial
        robot_info = None
        robot_status = None
        if detected_robot:
            robot_info = get_robot_info_from_portal(portal_conn, detected_robot)
            robot_status = get_robot_status_from_portal(portal_conn, detected_robot)

        # Create the Jira ticket
        ticket_url = create_qa_ticket(
            description=description.strip(),
            robot_serial=detected_robot,
            user_name=user_name,
            channel_name=channel_name,
            channel_id=channel_id,
            message_ts=message_ts,
            robot_info=robot_info,
            robot_status=robot_status
        )

        if not ticket_url:
            error_msg = "Error: Failed to create QA ticket. Please try again or contact support."
            return False, error_msg

        # Format the success message
        success_msg = f"QA ticket created successfully!\n{ticket_url}"
        if detected_robot:
            success_msg += f"\nRobot: {detected_robot}"
        success_msg += f"\nCreated by: @{user_name}"

        return True, success_msg

    except Exception as e:
        logger.error(f"Error in handle_qa_command: {e}")
        traceback.print_exc()
        return False, "Error: Failed to process QA command"
