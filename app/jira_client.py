import json
import logging
import requests
from datetime import datetime
from typing import Any, Optional
from .config import JIRA_BASE_URL, JIRA_USERNAME, JIRA_TOKEN

logger = logging.getLogger(__name__)



class JiraClient:
    def __init__(self):
        self.base_url = JIRA_BASE_URL.rstrip('/') if JIRA_BASE_URL else None
        self.username = JIRA_USERNAME
        self.token = JIRA_TOKEN
        
        assert JIRA_BASE_URL is not None, "JIRA_BASE_URL is not set"
        assert JIRA_USERNAME is not None, "JIRA_USERNAME is not set"
        assert JIRA_TOKEN is not None, "JIRA_TOKEN is not set"

        self.session = requests.Session()
        self.session.auth = (self.username, self.token)
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })

    def create_issue(self, project_key: str, issue_type: str, summary: str,
                    description: str, custom_fields: Optional[dict[str, Any]] = None) -> dict[str, Any]:
        url = f"{self.base_url}/rest/api/3/issue"

        # Build the issue data
        issue_data = {
            "fields": {
                "project": {"key": project_key},
                "issuetype": {"name": issue_type},
                "summary": summary
            }
        }

        if description:
            issue_data["fields"]["description"] = {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": description
                            }
                        ]
                    }
                ]
            }

        # Add custom fields if provided
        if custom_fields:
            issue_data["fields"].update(custom_fields)

        logger.info(f"Creating Jira issue in project {project_key}: {summary}")

        response = self.session.post(url, data=json.dumps(issue_data))

        if response.status_code == 201:
            issue = response.json()
            logger.info(f"Successfully created Jira issue: {issue['key']}")
            return issue
        else:
            error_msg = f"Failed to create Jira issue. Status: {response.status_code}, Response: {response.text}"
            logger.error(error_msg)
            raise requests.HTTPError(error_msg)

    def add_comment(self, issue_key: str, comment_text: str) -> dict[str, Any]:
        url = f"{self.base_url}/rest/api/3/issue/{issue_key}/comment"

        comment_data = {
            "body": {
                "type": "doc",
                "version": 1,
                "content": [
                    {
                        "type": "paragraph",
                        "content": [
                            {
                                "type": "text",
                                "text": comment_text
                            }
                        ]
                    }
                ]
            }
        }

        logger.info(f"Adding comment to Jira issue {issue_key}")

        response = self.session.post(url, data=json.dumps(comment_data))

        if response.status_code == 201:
            comment = response.json()
            logger.info(f"Successfully added comment to issue {issue_key}")
            return comment
        else:
            error_msg = f"Failed to add comment to issue {issue_key}. Status: {response.status_code}, Response: {response.text}"
            logger.error(error_msg)
            raise requests.HTTPError(error_msg)

    def get_issue_url(self, issue_key: str) -> str:
        return f"{self.base_url}/browse/{issue_key}"

    def search_issues(self, jql: str, max_results: int = 50) -> list[dict[str, Any]]:
        url = f"{self.base_url}/rest/api/3/search"

        params = {
            "jql": jql,
            "maxResults": max_results
        }

        logger.info(f"Searching Jira issues with JQL: {jql}")

        response = self.session.get(url, params=params)

        if response.status_code == 200:
            data = response.json()
            issues = data.get("issues", [])
            logger.info(f"Found {len(issues)} issues")
            return issues
        else:
            error_msg = f"Failed to search Jira issues. Status: {response.status_code}, Response: {response.text}"
            logger.error(error_msg)
            raise requests.HTTPError(error_msg)

    def get_issues_modified_since(self, since_datetime: datetime, projects: Optional[list[str]] = None) -> list[dict[str, Any]]:
        # Format datetime for JQL (YYYY-MM-DD HH:mm)
        since_str = since_datetime.strftime("%Y-%m-%d %H:%M")

        # Build JQL query
        jql_parts = [f'created >= "{since_str}"']

        if projects:
            project_filter = " OR ".join([f'project = "{p}"' for p in projects])
            jql_parts.append(f"({project_filter})")

        jql = " AND ".join(jql_parts)
        jql += " ORDER BY created DESC"

        logger.info(f"JQL: {jql}")

        return self.search_issues(jql, max_results=100)


def create_qa_ticket(description: str, robot_serial: Optional[str] = None, user_name: Optional[str] = None, channel_name: Optional[str] = None, channel_id: Optional[str] = None, message_ts: Optional[str] = None, robot_info: Optional[dict] = None, robot_status: Optional[dict] = None) -> str:
    client = JiraClient()

    # Build the summary
    summary = "QA Issue"
    if robot_serial:
        summary += f" - {robot_serial}"

    # Build the full description with context
    full_description = description

    # Add robot information from /info command if available
    if robot_info or robot_status or robot_serial or user_name or channel_name or (channel_id and message_ts):
        full_description += "\n\n--- Additional Information ---"

        if robot_serial:
            full_description += f"\nRobot: {robot_serial}"

        # Add detailed robot information similar to /info command
        if robot_info:
            if robot_info.get("software_version"):
                full_description += f"\nSoftware Version: {robot_info['software_version']}"

            if robot_info.get("model_id"):
                full_description += f"\nCurrent Model: {robot_info['model_id']}"

            feature_flags = robot_info.get("feature_flags", {})
            if feature_flags is None:
                feature_flags = {}

            embedding_enabled = feature_flags.get("embedding_based_classification_feature", None)
            if embedding_enabled is None:
                classification_method = "Unknown"
            elif embedding_enabled:
                classification_method = "Plant Profiles"
            else:
                classification_method = "Thresholds"

            full_description += f"\nClassification Method: {classification_method}"

        # Add robot status information if available
        if robot_status:
            from .info_command import format_time_ago

            # Last update time
            if robot_status.get("created_at"):
                time_ago = format_time_ago(robot_status["created_at"])
                full_description += f"\nLast Update: {time_ago}"

            # Combined status from field_config and last update time
            from datetime import datetime
            status = "Unknown"

            # Check if robot hasn't checked in for more than 10 minutes
            if robot_status.get("created_at"):
                timestamp_s = robot_status["created_at"] / 1000
                past_time = datetime.fromtimestamp(timestamp_s)
                now = datetime.now()
                time_diff = now - past_time
                minutes_since_update = time_diff.total_seconds() / 60

                if minutes_since_update > 10:
                    status = "Unknown"
                else:
                    # Robot has checked in recently, determine status from field_config
                    field_config = robot_status.get("field_config", {})
                    if field_config:
                        is_weeding = field_config.get("is_weeding", False)
                        is_thinning = field_config.get("is_thinning", False)

                        if is_weeding and is_thinning:
                            status = "Weeding & Thinning"
                        elif is_weeding:
                            status = "Weeding"
                        elif is_thinning:
                            status = "Thinning"
                        else:
                            status = "Standby"
                    else:
                        status = "Standby"

            full_description += f"\nStatus: {status}"

        if user_name:
            full_description += f"\nReported by: @{user_name}"
        if channel_id and message_ts:
            # Create a clickable Slack permalink to the original command message
            # Format: https://pono-maka.slack.com/archives/{channel_id}/{timestamp}
            slack_permalink = f"https://pono-maka.slack.com/archives/{channel_id}/{message_ts}"
            full_description += f"\nSlack Message: {slack_permalink}"
        elif channel_name:
            # Fallback to just channel name if no permalink info
            full_description += f"\nSlack Channel: #{channel_name}"

    # Prepare custom fields for assignee and labels
    custom_fields = {
        "assignee": {"emailAddress": "<EMAIL>"},
        "labels": ["qa"]
    }

    # Create the issue (using 'QA' project and 'Task' issue type as defaults)
    # These can be made configurable via environment variables if needed
    issue = client.create_issue(
        project_key="SOFTWARE",  # This should be configured based on your Jira setup
        issue_type="Bug",  # This can also be made configurable
        summary=summary,
        description=full_description,
        custom_fields=custom_fields
    )

    return client.get_issue_url(issue['key'])
